package main

import (
	"fmt"
	"wechatdll/comm"
	_ "wechatdll/routers"

	"github.com/astaxie/beego"
)

func main() {
	comm.RedisInitialize()
	_, err := comm.RedisClient.Ping().Result()
	if err != nil {
		fmt.Printf("【Redis】连接失败，ERROR：%v\n", err.Error())
		fmt.Println("程序将继续运行，但Redis相关功能可能不可用")
		// 可以选择退出程序或继续运行
		// os.Exit(1) // 如果Redis是必需的，可以取消注释这行
	}
	beego.BConfig.WebConfig.DirectoryIndex = true
	beego.BConfig.WebConfig.StaticDir["/"] = "swagger"
	beego.SetLogFuncCall(false)
	//自定义错误页面
	beego.Run()
}
