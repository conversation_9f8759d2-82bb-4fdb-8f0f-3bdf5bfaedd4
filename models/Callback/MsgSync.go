package Callback

func (T *Task) MsgSyncAdd(Data MsgSyncPushTask) {
	T.runningMu.Lock()
	defer T.runningMu.Unlock()
	entry := T.check(Data.Wxid)
	//判断任务是否为空
	if entry.Wxid == "" {
		T.serves = append(T.serves, &Serve{
			Wxid:      "",
			MsgSync:   false,
			Heartbeat: false,
		})
	}

	if entry.MsgSync.ID == 0 || entry.MsgSync.State == "STOPPED" {
		// TODO: 实现消息同步逻辑
		// go runMsgSync(Data.Wxid) // ✅ 暂时注释掉无用的goroutine启动
	}
}

func runMsgSync(wxid string) {
	// TODO: 实现具体的消息同步逻辑
}
