package TenPay

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
	"wechatdll/models/Tools"

	"github.com/forgoer/openssl"
	"github.com/golang/protobuf/proto"
)

type RealNameInfoResult struct {
	RetCode            int    `json:"retcode"`
	ErrText            string `json:"errText"`
	RealnameCenterhtml string `json:"realnameCenterhtml"`
}

func GetRealNameInfo(Wxid string) models.ResponseResult {
	D, err := comm.GetLoginata(Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.<PERSON>rror()),
			Data:    nil,
		}
	}

	reqTextData, err := SingWith3DES(strconv.FormatInt(time.Now().Unix(), 10))
	if err != nil {
		return models.ResponseResult{
			Code:    -9,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	req := &mm.TenPayRequest{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    D.Sessionkey,
			Uin:           proto.Uint32(D.Uin),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(D.DeviceType),
			Scene:         proto.Uint32(0),
		},
		CgiCmd:     proto.Uint32(1654),
		OutPutType: proto.Uint32(1),
		ReqText: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(reqTextData))),
			Buffer: []byte(reqTextData),
		},
		ReqTextWx: nil,
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	Host := comm.GetIp(*D)
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/mmpay-bin/tenpay/paymanage",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              1654,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      true,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	Response := mm.TenPayResponse{}
	err = proto.Unmarshal(protobufdata, &Response)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	result := RealNameInfoResult{}

	if Response.BaseResponse.GetRet() != 0 {
		result.RetCode = -1
		result.ErrText = Response.BaseResponse.ErrMsg.String()
		return models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: "失败",
			Data:    result,
		}
	}

	if Response.GetPlatRet() != 0 {
		result.RetCode = -1
		result.ErrText = Response.GetPlatMsg()
		return models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: "失败",
			Data:    result,
		}
	}

	if Response.GetTenpayErrType() != 0 {
		result.RetCode = -1
		result.ErrText = Response.GetTenpayErrMsg()
		return models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: "失败",
			Data:    result,
		}
	}

	retTextStr := Response.RetText.GetBuffer()

	type RetText struct {
		RetCode     string `json:"retcode"`
		RetMsg      string `json:"retmsg"`
		RealNameUrl string `json:"realname_url"`
	}

	retText := &RetText{}

	err = json.Unmarshal([]byte(retTextStr), retText)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("retTextStr反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}
	if retText.RetCode != "0" {
		result.RetCode = -1
		result.ErrText = retText.RetMsg
		return models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: "失败",
			Data:    result,
		}
	}

	if retText.RealNameUrl == "" {
		result.RetCode = 0
		result.ErrText = "未实名"
		return models.ResponseResult{
			Code:    -1,
			Success: false,
			Message: "失败",
			Data:    result,
		}
	}

	getA8KeyResult := Tools.GetA8Key(Tools.GetA8KeyParam{
		Wxid:        Wxid,
		OpCode:      2,
		Scene:       0,
		CodeType:    0,
		CodeVersion: 0,
		ReqUrl:      retText.RealNameUrl,
	})

	ret := getA8KeyResult.Data
	switch value := ret.(type) {
	case mm.GetA8KeyResp:
		if value.BaseResponse.GetRet() != 0 {
			result.RetCode = -1
			result.ErrText = value.BaseResponse.ErrMsg.String()
			return models.ResponseResult{
				Code:    -1,
				Success: false,
				Message: "失败",
				Data:    result,
			}
		}

		url := value.GetFullURL()
		if url == "" {
			return models.ResponseResult{
				Code:    -10,
				Success: false,
				Message: "请求GetA8Key失败",
				Data:    result,
			}
		}

		statuscode, redirecturl, _, err := HttpGetRead(url)
		if err != nil {
			return models.ResponseResult{
				Code:    -10,
				Success: false,
				Message: fmt.Sprintf("请求GetA8Key中的url失败,err: %v", err),
				Data:    result,
			}
		}

		if statuscode == 302 || statuscode == 301 {
			if strings.Contains(redirecturl, "open.weixin.qq.com") {
				oAuthAuthorizeResult := OauthAuthorize(Wxid, redirecturl, "")

				data := oAuthAuthorizeResult.Data
				switch value := data.(type) {
				case mm.OauthAuthorizeResp:
					redirect := value.GetRedirectUrl()
					if redirect == "" {
						return models.ResponseResult{
							Code:    -10,
							Success: false,
							Message: fmt.Sprintf("请求OauthAuthorize接口获取redirecturl失败,err:%v", err),
							Data:    result,
						}
					}

					_, _, Html, err := HttpGetRead(redirect)
					if err != nil {
						return models.ResponseResult{
							Code:    -10,
							Success: false,
							Message: fmt.Sprintf("请求OauthAuthorize中的RedirectUrl失败,err:%v", err),
							Data:    result,
						}
					}

					result.RetCode = 0
					result.ErrText = "ok"
					result.RealnameCenterhtml = Html
					return models.ResponseResult{
						Code:    0,
						Success: true,
						Message: "成功",
						Data:    result,
					}
				}

			}
		}
	}
	result.RetCode = -1
	result.ErrText = "未知错误"
	return models.ResponseResult{
		Code:    -1,
		Success: false,
		Message: "失败",
		Data:    result,
	}
}

func SingWith3DES(Src string) (string, error) {
	buf := md5.New()
	buf.Write([]byte(Src))
	k := buf.Sum(nil)
	bcd := make([]byte, 32)
	for i := 0; i < len(k); i++ {
		bcd[2*i] = k[i] >> 4
		bcd[2*i+1] = k[i] & 0xf
	}
	tkey := []byte(hex.EncodeToString(k))
	key := make([]byte, 24, 24)
	copy(key, tkey)
	Data, err := openssl.Des3ECBEncrypt(bcd, key, "")
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(Data), nil
}

func HttpGetRead(baseHost string) (int, string, string, error) {
	client := &http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
	}

	reqest, err := http.NewRequest("GET", baseHost, nil) //建立一个请求
	if err != nil {
		return 0, "", "", err
	}

	reqest.Header.Add("Accept", "*/*")
	reqest.Header.Add("Content-Type", "application/octet-stream")
	reqest.Header.Add("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/7.0.4(0x17000428) NetType/4G Language/zh_CN")
	reqest.Header.Add("Connection", "keep-alive")

	response, err := client.Do(reqest) //提交

	if err != nil {
		return 0, "", "", err
	}

	defer response.Body.Close()
	body, err1 := ioutil.ReadAll(response.Body)
	if err1 != nil {
		return 0, "", "", err1
	}

	if response.StatusCode == 301 || response.StatusCode == 302 {
		return response.StatusCode, response.Header.Get("Location"), string(body), nil
	} else {
		return response.StatusCode, baseHost, string(body), nil
	}
}
