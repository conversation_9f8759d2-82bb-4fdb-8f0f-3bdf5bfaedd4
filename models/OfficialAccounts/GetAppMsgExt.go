package OfficialAccounts

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
	"wechatdll/Cilent/mm"
	"wechatdll/bts"
	"wechatdll/models"

	log "github.com/sirupsen/logrus"
)

func GetAppMsgExt(Data ReadParam) models.ResponseResult {
	//获取mp-geta8key
	geta8key := MpGetA8Key(Data)
	if geta8key.Success == false {
		return geta8key
	}

	GetA8KEY := bts.GetA8KeyResponse(geta8key.Data)
	if GetA8KEY.FullURL == nil || *GetA8KEY.HttpHeaderCount < 2 {
		return models.ResponseResult{
			Code:    -9,
			Success: false,
			Message: "异常,文章解析失败。",
			Data:    GetA8KEY,
		}
	}

	//解析
	Url2, err := url.Parse(*GetA8KEY.FullURL)
	_, b := GetHTML(*GetA8KEY.FullURL, GetA8KEY.GetHttpHeader())
	devicetype := ""
	lang := "zh_CN"
	pass_ticket := ""
	rewardsn := "rewardsn=;"
	version := ""
	wap_sid2 := ""
	wxtokenkey := "wxtokenkey=777;"
	wxuin := ""
	appmsgToken := ""
	for _, v := range b {
		appmsgTokenT := strings.Index(v, "appmsg_token=")
		devicetypeT := strings.Index(v, "devicetype=")
		langT := strings.Index(v, "lang=")
		pass_ticketT := strings.Index(v, "pass_ticket=")
		versionT := strings.Index(v, "version=")
		wap_sid2T := strings.Index(v, "wap_sid2=")
		wxtokenkeyT := strings.Index(v, "wxtokenkey=")
		wxuinT := strings.Index(v, "wxuin=")
		if devicetypeT != -1 {
			devicetype = strings.Replace(v, "Path=/", "", 1)
		} else if langT != -1 {
			lang = strings.Replace(v, " Path=/", "", 1)
		} else if pass_ticketT != -1 {
			pass_ticket = strings.Replace(v, " Path=/; HttpOnly", "", 1)
		} else if versionT != -1 {
			version = strings.Replace(v, " Path=/", "", 1)
		} else if wap_sid2T != -1 {
			wap_sid2 = strings.Replace(v, " Path=/; HttpOnly", "", 1)
		} else if wxtokenkeyT != -1 {
			wxtokenkey = strings.Replace(v, " Path=/; HttpOnly", "", 1)
		} else if wxuinT != -1 {
			wxuin = strings.Replace(v, "; Path=/; HttpOnly", "", 1)
		} else if appmsgTokenT != -1 {
			appmsgToken = strings.Replace(v, "appmsg_token=", "", 1)
		}
	}
	cookeStr := devicetype + lang + pass_ticket + rewardsn + version + wap_sid2 + wxtokenkey + wxuin
	fmt.Println(cookeStr)
	appmsgToken = strings.Replace(appmsgToken, "; Path=/", "", 1)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    GetA8KEY,
		}
	}
	JX2 := Url2.Query()
	var PostUrl string
	var PostData string

	defer func() {
		log.Error("GetAppMsgExt:", recover())
	}()

	// 固定使用iPad设备类型
	const fixedDeviceType = "iOS14.2"
	const fixedClientVersion = "1800012a"

	PostUrl = "https://mp.weixin.qq.com/mp/getappmsgext?f=json&mock=&uin=777&key=777&pass_ticket=" + JX2["pass_ticket"][0] + "&wxtoken=777&devicetype=" + fixedDeviceType + "&clientversion=" + fixedClientVersion + "&__biz=" + url.QueryEscape(JX2["__biz"][0]) + "&appmsg_token=" + appmsgToken + "&x5=0&f=json"
	//PostUrl = "https://mp.weixin.qq.com/mp/getappmsgext?f=json&mock=&fasttmplajax=1&f=json&uin=777&key=777&pass_ticket=" + JX2["pass_ticket"][0] + "&wxtoken=777&devicetype=iOS14.2&clientversion=1800012a&__biz=" + url.QueryEscape(JX2["__biz"][0]) + "&appmsg_token=&x5=0&f=json&wx_header=1&pass_ticket=" + JX2["pass_ticket"][0]
	PostData = "r=&__biz=" + url.QueryEscape(JX2["__biz"][0]) + "&appmsg_type=6&mid=" + JX2["mid"][0] + "&sn=" + JX2["sn"][0] + "&idx=" + JX2["idx"][0] + "&scene=&title=&ct=" + time.Now().String() + "&abtest_cookie=&devicetype=" + fixedDeviceType + "&version=" + fixedClientVersion + "&is_need_ticket=0&is_need_ad=0&comment_id=&is_need_reward=0&both_ad=0&reward_uin_count=0&send_time=&msg_daily_idx=1&is_original=0&is_only_read=1&req_id=&pass_ticket=" + JX2["pass_ticket"][0] + "&is_temp_url=0&item_show_type=0&tmp_version=1&more_read_type=0&appmsg_like_type=2&related_video_sn=&vid=&is_pay_subscribe=0&pay_subscribe_uin_count=0&has_red_packet_cover=0&album_id="
	fmt.Println(PostUrl)
	fmt.Println(PostData)
	client := &http.Client{}
	req, err := http.NewRequest("POST", PostUrl, strings.NewReader(PostData))
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    GetA8KEY,
		}
	}
	req.Header.Set("Host", "mp.weixin.qq.com")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 13_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/7.0.12(0x1800012a) NetType/WIFI Language/zh_CN")
	for _, v := range GetA8KEY.HttpHeader {
		if v != nil {
			req.Header.Set(v.GetKey(), v.GetValue())
		}
	}
	req.Header.Set("Cookie", cookeStr)
	resp, err := client.Do(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    GetA8KEY,
		}
	}

	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    GetA8KEY,
		}
	}
	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    string(body),
	}
}

/*
**
获取微信html页面
*/
func GetHTML(url string, headList []*mm.HttpHeader) (string, []string) {
	client := &http.Client{}
	reqest, _ := http.NewRequest("GET", url, nil)
	reqest.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	reqest.Header.Set("Accept-Charset", "utf-8;q=0.7,*;q=0.3")
	//reqest.Header.Set("Accept-Encoding", "gzip, default")//这个有乱码，估计是没有解密，或解压缩
	reqest.Header.Set("Accept-Encoding", "utf-8") //这就没有乱码了
	reqest.Header.Set("Accept-Language", "zh-cn,zh;q=0.8,en-us;q=0.5,en;q=0.3")
	reqest.Header.Set("Cache-Control", "max-age=0")
	reqest.Header.Set("Connection", "keep-alive")
	reqest.Header.Set("Host", url)
	reqest.Header.Set("User-Agent", "MMozilla/5.0 (iPhone; CPU iPhone OS 5_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9B176 MicroMessenger/4.3.2")
	for _, head := range headList {
		if head != nil {
			reqest.Header.Add(head.GetKey(), head.GetValue())
		}
	}
	response, _ := client.Do(reqest)
	if response != nil {
		defer response.Body.Close()
		if response.StatusCode == 200 {
			body, _ := ioutil.ReadAll(response.Body)
			bodystr := string(body)
			v := response.Header
			b := v["Set-Cookie"]
			return bodystr, b //response.Header..Get("Set-Cookie")
		}
	}
	return "", nil
}
