package Login

import (
	"container/list"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"log"
	"os"
	"strings"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/lib"
	"wechatdll/models"

	"github.com/astaxie/beego"
	"github.com/golang/protobuf/proto"
)

func AndroidA16Login(Data A16LoginParam, domain string) models.ResponseResult {

	DeviceInfo := Data.Extend

	//初始化Mmtls
	httpclient, MmtlsClient, err := comm.MmtlsInitialize(Data.Proxy)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("MMTLS初始化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	//获取DeviceToken
	DeviceToken, err := AndroidGetDeviceToken(Data.A16[:15], Data.Extend, *httpclient, Data.Proxy)
	if err != nil {
		DeviceToken = mm.TrustResponse{}
	}

	T := time.Now().Unix()

	Deviceid := []byte(Data.A16[:15])
	passwordhash := md5.Sum([]byte(Data.Password))
	prikey, pubkey := Algorithm.GetEcdh713Key()

	Wcstf := Algorithm.IphoneWcstf07(Data.UserName)
	Wcste := Algorithm.IphoneWcste07(0, 0)
	AndroidCcData := Algorithm.AndroidCcData(Data.A16, Data.Extend, DeviceToken, T)
	CcData3PB, _ := proto.Marshal(AndroidCcData)

	curtime := uint32(T)
	DeviceTokenCCD := &mm.DeviceToken{
		Version:   proto.String(""),
		Encrypted: proto.Uint32(1),
		Data: &mm.SKBuiltinStringT{
			String_: proto.String(DeviceToken.GetTrustResponseData().GetDeviceToken()),
		},
		TimeStamp: &curtime,
		Optype:    proto.Uint32(2),
		Uin:       proto.Uint32(0),
	}
	DeviceTokenCCDPB, _ := proto.Marshal(DeviceTokenCCD)

	WCExtInfo := &mm.WCExtInfo{
		Wcstf: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(Wcstf))),
			Buffer: Wcstf,
		},
		Wcste: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(Wcste))),
			Buffer: Wcste,
		},
		CcData: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(CcData3PB))),
			Buffer: CcData3PB,
		},
		DeviceToken: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(DeviceTokenCCDPB))),
			Buffer: DeviceTokenCCDPB,
		},
	}

	WCExtInfoPB, _ := proto.Marshal(WCExtInfo)

	aeskey := []byte(lib.RandSeq(16))

	secmanualauth := &mm.SecManualLoginRequest{
		RsaReqData: &mm.ManualAuthRsaReqData{
			RandomEncryKey: &mm.SKBuiltinBufferT{
				ILen:   proto.Uint32(uint32(len(aeskey))),
				Buffer: aeskey,
			},
			CliPubEcdhkey: &mm.ECDHKey{
				Nid: proto.Int32(713),
				Key: &mm.SKBuiltinBufferT{
					ILen:   proto.Uint32(uint32(len(pubkey))),
					Buffer: pubkey,
				},
			},
			UserName: proto.String(Data.UserName),
			Pwd:      proto.String(hex.EncodeToString(passwordhash[:])),
			Pwd2:     proto.String(hex.EncodeToString(passwordhash[:])),
		},
		AesReqData: &mm.ManualAuthAesReqData{
			BaseRequest: &mm.BaseRequest{
				SessionKey:    []byte{},
				Uin:           proto.Uint32(0),
				DeviceId:      Deviceid,
				ClientVersion: proto.Int32(int32(Algorithm.AndroidVersion)),
				DeviceType:    []byte(Algorithm.AndroidDeviceType),
				Scene:         proto.Uint32(1),
			},
			Imei:         proto.String(DeviceInfo.AndriodImei(Data.A16)),
			SoftType:     proto.String(DeviceInfo.AndriodGetSoftType(Data.A16)),
			BuiltinIpseq: proto.Uint32(0),
			ClientSeqId:  proto.String(fmt.Sprintf("%s_%d", Data.A16, (time.Now().UnixNano() / 1e6))),
			Signature:    proto.String(DeviceInfo.AndriodPackageSign(Data.A16)),
			DeviceName:   proto.String(DeviceInfo.AndroidManufacturer(Data.A16) + "-" + DeviceInfo.AndroidPhoneModel(Data.A16)),
			DeviceType:   proto.String(DeviceInfo.AndriodDeviceType(Data.A16)),
			Language:     proto.String("zh_CN"),
			TimeZone:     proto.String("8.00"),
			Channel:      proto.Int32(0),
			TimeStamp:    proto.Uint32(0),
			DeviceBrand:  proto.String("google"),
			DeviceModel:  proto.String(DeviceInfo.AndroidPhoneModel(Data.A16) + DeviceInfo.AndroidArch(Data.A16)),
			Ostype:       proto.String(Algorithm.AndroidDeviceType),
			RealCountry:  proto.String(""),
			InputType:    proto.Uint32(2),
			ExtSpamInfo: &mm.SKBuiltinBufferT{
				ILen:   proto.Uint32(uint32(len(WCExtInfoPB))),
				Buffer: WCExtInfoPB,
			},
		},
	}

	reqdata, _ := proto.Marshal(secmanualauth)
	hec := &Algorithm.Client{}
	hec.Init("Android")
	hecData := hec.HybridEcdhPackAndroidEn(252, 10002, 0, nil, reqdata)
	recvData, err := httpclient.MMtlsPost(domain, "/cgi-bin/micromsg-bin/secmanualauth", hecData, Data.Proxy)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	if len(recvData) <= 31 {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: "微信返回的数据存在异常",
			Data:    hex.EncodeToString(recvData),
		}
	}

	ph1 := hec.HybridEcdhPackAndroidUn(recvData)
	loginRes := mm.UnifyAuthResponse{}
	err = proto.Unmarshal(ph1.Data, &loginRes)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	ExecutionLog, _ := beego.AppConfig.Bool("ExecutionLog")

	if ExecutionLog {
		T := time.Now().Format("2006010215")
		logFile, err := os.OpenFile(fmt.Sprintf("log/%v_login.txt", T), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0755)
		if err == nil {
			defer logFile.Close() // ✅ 修复文件句柄泄露
			log.SetFlags(0)
			loger := log.New(logFile, "", log.Flags())
			if loginRes.GetBaseResponse().GetRet() != -301 && loginRes.GetBaseResponse().GetRet() != -305 {
				loger.Printf("%v----%v----%v----%v", Data.UserName, Data.Password, Data.A16, Data.Proxy.ProxyIp)
			}
		}
	}

	if loginRes.GetBaseResponse().GetRet() == 0 {
		var LoginData comm.LoginData
		LoginData.Cooike = ph1.Cookies
		LoginData.MmtlsHost = domain
		LoginData.Deviceid_str = Data.A16
		LoginData.Deviceid_byte = Deviceid
		LoginData.MmtlsKey = MmtlsClient
		LoginData.ClientVersion = Algorithm.AndroidVersion
		LoginData.DeviceType = Algorithm.AndroidDeviceType

		ecdhkey := Algorithm.DoECDH713(loginRes.GetAuthSectResp().GetSvrPubEcdhkey().GetKey().GetBuffer(), prikey)
		LoginData.Loginecdhkey = ecdhkey
		LoginData.Uin = loginRes.GetAuthSectResp().GetUin()
		LoginData.Wxid = loginRes.GetAcctSectResp().GetUserName()
		LoginData.Alais = loginRes.GetAcctSectResp().GetAlias()
		LoginData.Mobile = loginRes.GetAcctSectResp().GetBindMobile()
		LoginData.NickName = loginRes.GetAcctSectResp().GetNickName()
		LoginData.Sessionkey = Algorithm.AESDecrypt(loginRes.GetAuthSectResp().GetSessionKey().GetBuffer(), ecdhkey)
		LoginData.Sessionkey_2 = loginRes.GetAuthSectResp().GetSessionKey().GetBuffer()
		LoginData.Autoauthkey = loginRes.GetAuthSectResp().GetAutoAuthKey().GetBuffer()
		LoginData.Autoauthkeylen = int32(loginRes.GetAuthSectResp().GetAutoAuthKey().GetILen())
		LoginData.Serversessionkey = loginRes.GetAuthSectResp().GetServerSessionKey().GetBuffer()
		LoginData.Clientsessionkey = loginRes.GetAuthSectResp().GetClientSessionKey().GetBuffer()

		var Dns []comm.Dns
		//记录DNSip
		ip_info := loginRes.GetNetworkSectResp().GetBuiltinIplist().GetShortConnectIplist()
		for _, v := range ip_info {
			IP := strings.Replace(v.GetIp(), string(byte(0x00)), "", -1)
			Host := strings.Replace(v.GetHost(), string(byte(0x00)), "", -1)
			if IP != "127.0.0.1" && Host != "localhost" && strings.Index(Host, "pay") == -1 {
				Dns = append(Dns, comm.Dns{
					Ip:   IP,
					Host: Host,
				})
			}
		}
		LoginData.Dns = Dns

		err := comm.CreateLoginData(LoginData, LoginData.Wxid, 0)

		if err != nil {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("系统异常：%v", err.Error()),
				Data:    nil,
			}
		}

		return models.ResponseResult{
			Code:    0,
			Success: true,
			Message: "成功",
			Data:    loginRes,
		}
	}

	//30系列转向
	if loginRes.GetBaseResponse().GetRet() == -301 {
		var Wx_newLong_Host, Wx_newshort_Host, Wx_newshortext_Host list.List

		dns_info := loginRes.GetNetworkSectResp().GetNewHostList().GetList()
		for _, v := range dns_info {
			if v.GetHost() == "long.weixin.qq.com" {
				ip_info := loginRes.GetNetworkSectResp().GetBuiltinIplist().GetLongConnectIplist()
				for _, ip := range ip_info {
					host := ip.GetHost()
					host = strings.Replace(host, string(byte(0x00)), "", -1)
					if host == v.GetRedirect() {
						Wx_newLong_Host.PushBack(host)
					}
				}
			} else if v.GetHost() == "short.weixin.qq.com" {
				ip_info := loginRes.GetNetworkSectResp().GetBuiltinIplist().GetShortConnectIplist()
				for _, ip := range ip_info {
					host := ip.GetHost()
					host = strings.Replace(host, string(byte(0x00)), "", -1)
					if host == v.GetRedirect() {
						Wx_newshort_Host.PushBack(host)
					}
				}
			} else if v.GetHost() == "extshort.weixin.qq.com" {
				ip_info := loginRes.GetNetworkSectResp().GetBuiltinIplist().GetShortConnectIplist()
				for _, ip := range ip_info {
					host := ip.GetHost()
					host = strings.Replace(host, string(byte(0x00)), "", -1)
					if host == v.GetRedirect() {
						Wx_newshortext_Host.PushBack(host)
					}
				}
			}
		}
		return AndroidA16Login(Data, Wx_newshort_Host.Front().Value.(string))
	}

	return models.ResponseResult{
		Code:    -8,
		Success: false,
		Message: "失败",
		Data:    loginRes,
	}
}
