package Algorithm

import (
	"crypto/elliptic"
	"hash"
)

// 0x17000841 IOS 708
// 0x17000C2B IOS 712
//0x17000E24  IOS 714
//浏览器版本
//[]byte("Windows-QQBrowser")

var MMtls_host = "extshort.weixin.qq.com" //"szshort.weixin.qq.com"

// 设备类型
var IPadDeviceType = "iPad iOS14.2"

var WinDeviceType = "Windows 10 x64"
var IPhoneDeviceType = "iPhone iOS13.5"
var AndroidDeviceType = "android-27"
var MacDeviceType = "iMac MacBookPro16,1 OSX OSX10.15.4 build(19E287)"

//版本号
//var IPadVersion = 0x18000722 //0x17000841

var IPadVersion = 0x18003B22 //8.5.8 //0x1800322d //402665771 //402656549//402655025 // 402654754
var WinVersion = 0x68cdc296
var IPhoneVersion = 0x18003B22 //402654754
var AndroidVersion = 671089460 //803
var MacVersion = 62090070

type HYBRID_STATUS int32

const (
	HYBRID_ENC HYBRID_STATUS = 0
	HYBRID_DEC HYBRID_STATUS = 1
)

type Client struct {
	PubKey     []byte
	Privkey    []byte
	InitPubKey []byte
	Externkey  []byte

	Version    int
	DeviceType string

	clientHash hash.Hash
	serverHash hash.Hash

	curve elliptic.Curve

	Status HYBRID_STATUS
}

type PacketHeader struct {
	PacketCryptType byte
	Flag            uint16
	RetCode         uint32
	UICrypt         uint32
	Uin             uint32
	Cookies         []byte
	Data            []byte
}

type PackData struct {
	Reqdata          []byte
	Cgi              int
	Uin              uint32
	Cookie           []byte
	ClientVersion    int
	Sessionkey       []byte
	EncryptType      uint8
	Loginecdhkey     []byte
	Clientsessionkey []byte
	Serversessionkey []byte
	UseCompress      bool
	MMtlsClose       bool
}
