package Mmtls

import (
	"bytes"
	"fmt"
	"golang.org/x/net/proxy"
	"io/ioutil"
	"math/rand"
	"net"
	"net/http"
	"net/url"
	"time"
	"wechatdll/models"
)

func (httpclient *HttpClientModel) POST(cgiurl string, data []byte, host string, P models.ProxyInfo) ([]byte, error) {
	var iphost string
	var err error
	iphost = "http://"
	iphost += host
	iphost += cgiurl
	body := bytes.NewReader(data)

	var Client *http.Client
	//设定代理
	if P.ProxyIp != "" && P.ProxyIp != "string" {
		if P.IsHttp {
			url := url.URL{}
			urlProxy, _ := url.Parse(P.ProxyIp)
			Client = &http.Client{
				Transport: &http.Transport{
					Proxy: http.ProxyURL(urlProxy),
				},
			}
		} else {
			var ProxyUser *proxy.Auth
			//设定账号和用户名
			if P.ProxyUser != "" && P.ProxyUser != "string" && P.ProxyPassword != "" && P.ProxyPassword != "string" {
				ProxyUser = &proxy.Auth{
					User:     P.ProxyUser,
					Password: P.ProxyPassword,
				}
			} else {
				ProxyUser = nil
			}
			Client, err = Socks5Client(P.ProxyIp, ProxyUser)
			if err != nil {
				return []byte{}, err
			}
		}
	} else {
		Client = &http.Client{
			Transport: &http.Transport{
				Dial: func(netw, addr string) (net.Conn, error) {
					conn, err := net.DialTimeout(netw, addr, time.Second*10) //设置建立连接超时
					if err != nil {
						return nil, err
					}
					// conn.SetDeadline(time.Now().Add(time.Second * 10)) // 修复: 删除Deadline避免CLOSE_WAIT //设置发送接受数据超时
					return conn, nil
				},
				ResponseHeaderTimeout: time.Second * 10,
				MaxIdleConnsPerHost:   -1,   //禁用连接池缓存
				DisableKeepAlives:     true, //禁用客户端连接缓存到连接池
			},
		}
	}
	//fmt.Println("88888888", iphost)
	request, err := http.NewRequest("POST", iphost, body)
	if err != nil {
		return []byte(""), err
	}
	//request.Header.Set("X-Forwarded-For",genIpaddr())//每次请求ip不一样
	request.Header.Set("Accept", "*/*")
	request.Header.Set("Cache-Control", "no-cache")
	request.Header.Set("Connection", "close")
	request.Header.Set("Content-type", "application/octet-stream")
	request.Header.Set("User-Agent", "MicroMessenger Client")
	request.Close = true
	var resp *http.Response
	resp, err = Client.Do(request)
	if err != nil {
		return []byte(""), err
	}
	defer resp.Body.Close()
	b, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return []byte(""), err
	}
	return b, nil
}
func genIpaddr() string {
	rand.Seed(time.Now().Unix())
	ip := fmt.Sprintf("%d.%d.%d.%d", rand.Intn(255), rand.Intn(255), rand.Intn(255), rand.Intn(255))
	return ip
}

func Socks5Client(addr string, auth *proxy.Auth) (client *http.Client, err error) {
	dialer, err := proxy.SOCKS5("tcp", addr,
		auth,
		&net.Dialer{
			Timeout:  10 * time.Second,
			// Deadline: time.Now().Add(time.Second * 10), // 修复: 删除Deadline避免CLOSE_WAIT
		},
	)
	if err != nil {
		return nil, err
	}

	transport := &http.Transport{
		Proxy:               nil,
		Dial:                dialer.Dial,
		TLSHandshakeTimeout: 10 * time.Second,
		MaxIdleConnsPerHost: -1,   //连接池禁用缓存
		DisableKeepAlives:   true, //禁用客户端连接缓存到连接池
	}

	client = &http.Client{Transport: transport}
	return
}
